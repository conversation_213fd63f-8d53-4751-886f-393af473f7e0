package vn.osp.common.domain.exceptions;

import vn.osp.common.domain.constants.ErrorCodesConst;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * Exception được ném khi có conflict về duplicate data.
 * Thường xảy ra khi vi phạm unique constraints trong database.
 * Tương ứng với HTTP 409 Conflict status code.
 */
public class DuplicateEntityException extends DomainException {

    /**
     * Khởi tạo duplicate entity exception với entity name và conflicting value.
     *
     * <pre>
     * throw new DuplicateEntityException("User", email);
     * throw new DuplicateEntityException("Product", productCode);
     * </pre>
     *
     * @param entityName       tên entity bị duplicate
     * @param conflictingValue giá trị bị duplicate
     */
    public DuplicateEntityException(String entityName, Object conflictingValue) {
        super(
                ErrorCodesConst.ENTITY_ALREADY_EXISTS,
                entityName + " with value '" + conflictingValue + "' already exists.",
                entityName, conflictingValue
        );
    }

    /**
     * Khởi tạo với specific field name và conflicting value.
     *
     * <pre>
     * throw new DuplicateEntityException("User", "Email", email);
     * throw new DuplicateEntityException("Order", "OrderNumber", orderNumber);
     * </pre>
     *
     * @param entityName       tên entity
     * @param fieldName        tên field bị duplicate
     * @param conflictingValue giá trị bị duplicate
     */
    public DuplicateEntityException(String entityName, String fieldName, Object conflictingValue) {
        super(
                ErrorCodesConst.ENTITY_ALREADY_EXISTS,
                entityName + " with " + fieldName + " '" + conflictingValue + "' already exists.",
                entityName, fieldName, conflictingValue
        );
    }

    /**
     * Khởi tạo với multiple conflicting fields.
     *
     * <pre>
     * Map.of("Email", email, "Phone", phone);
     * throw new DuplicateEntityException("User", conflicts);
     * </pre>
     *
     * @param entityName        tên entity
     * @param conflictingFields map các fields bị conflict
     */
    public DuplicateEntityException(String entityName, Map<String, Object> conflictingFields) {
        super(
                ErrorCodesConst.ENTITY_ALREADY_EXISTS,
                entityName + " with " + conflictingFields.entrySet().stream()
                        .map(e -> e.getKey() + "='" + e.getValue() + "'")
                        .collect(Collectors.joining(", ")) + " already exists.",
                entityName, conflictingFields
        );
    }

    /**
     * Khởi tạo với custom message và optional parameters.
     *
     * <pre>
     * throw new DuplicateEntityException("User with this email already exists in the system", "User", email);
     * </pre>
     *
     * @param customMessage thông điệp tùy chỉnh
     * @param parameters    các parameters bổ sung
     */
    public DuplicateEntityException(String customMessage, Object... parameters) {
        super(ErrorCodesConst.ENTITY_ALREADY_EXISTS, customMessage, parameters);
    }

    /**
     * Tạo exception cho unique constraint violation.
     *
     * @param entityName       tên entity
     * @param constraintField  field vi phạm constraint
     * @param conflictingValue giá trị bị conflict
     * @return DuplicateEntityException với error code phù hợp
     */
    public static DuplicateEntityException createForUniqueConstraint(String entityName, String constraintField, Object conflictingValue) {
        return new DuplicateEntityException(
                ErrorCodesConst.UNIQUE_CONSTRAINT_VIOLATION,
                "Unique constraint violation: " + entityName + " with " + constraintField + " '" + conflictingValue + "' already exists.",
                entityName,
                constraintField,
                conflictingValue
        );
    }

    /**
     * Tạo exception cho composite key violation.
     *
     * @param entityName tên entity
     * @param keyValues  map chứa composite key values
     * @return DuplicateEntityException với error code phù hợp
     */
    public static DuplicateEntityException createForCompositeKey(String entityName, Map<String, Object> keyValues) {
        String keyDescription = keyValues.entrySet().stream()
                .map(e -> e.getKey() + "='" + e.getValue() + "'")
                .collect(Collectors.joining(", "));

        return new DuplicateEntityException(
                ErrorCodesConst.COMPOSITE_KEY_VIOLATION,
                "Composite key violation: " + entityName + " with key [" + keyDescription + "] already exists.",
                entityName,
                keyValues
        );
    }

    /**
     * Internal constructor với custom error code.
     */
    private DuplicateEntityException(String errorCode, String message, Object... parameters) {
        super(errorCode, message, parameters);
    }
}