name: <PERSON><PERSON><PERSON> bộ global-instructions.md sang các file hướng dẫn

on:
  pull_request:
    paths:
      - "global-instructions.md"

jobs:
  sync-instructions:
    if: ${{ github.event.pull_request.head.repo.full_name == github.repository }}
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
      - name: <PERSON><PERSON><PERSON> <PERSON>ra mã ng<PERSON><PERSON><PERSON>
        uses: actions/checkout@v4
        with:
          ref: ${{ github.head_ref }}

      - name: <PERSON>o chép global-instructions.md sang AGENTS.md
        run: cp global-instructions.md AGENTS.md

      - name: Sao chép global-instructions.md sang CLAUDE.md
        run: cp global-instructions.md CLAUDE.md

      - name: Sao chép global-instructions.md sang GEMINI.md
        run: cp global-instructions.md GEMINI.md

      - name: Sao chép global-instructions.md sang copilot-instructions.md
        run: mkdir -p .github && cp global-instructions.md .github/copilot-instructions.md

      - name: <PERSON><PERSON> ché<PERSON> global-instructions.md sang augment-instructions.md
        run: mkdir -p .augment/rules/imported && cp global-instructions.md .augment/rules/imported/augment-instructions.md

      - name: <PERSON><PERSON><PERSON> h<PERSON>nh git user
        run: |
          git config user.name "github-actions[bot]"
          git config user.email "41898282+github-actions[bot]@users.noreply.github.com"

      - name: Thêm các file hướng dẫn
        run: git add AGENTS.md CLAUDE.md GEMINI.md .github/copilot-instructions.md .augment/rules/imported/augment-instructions.md

      - name: Commit thay đổi
        run: git commit -m "Đồng bộ các file hướng dẫn với global-instructions.md" || echo "No changes to commit"

      - name: Đẩy thay đổi lên remote
        run: git push
