name: "<PERSON><PERSON><PERSON> gi<PERSON> thay đổi yêu cầu nghiệp vụ"

on:
  pull_request:
    types: [closed]
    branches:
      - main
      - develop
    paths:
      - "docs/requirements/**"
  workflow_dispatch:
    inputs:
      scan_mode:
        description: "Chế độ quét"
        required: true
        default: "auto"
        type: choice
        options:
          - "auto"
          - "full"
          - "custom"
      from_commit:
        description: "Commit bắt đầu (chỉ dùng cho chế độ custom)"
        required: false
        type: string
      to_commit:
        description: "Commit kế<PERSON> thúc (chỉ dùng cho chế độ custom, mặc định là HEAD)"
        required: false
        type: string
        default: "HEAD"
      force_create_issue:
        description: "Bắt buộc tạo issue ngay cả khi không có thay đổi mới"
        required: false
        type: boolean
        default: false

permissions:
  contents: read
  issues: write
  pull-requests: write
  actions: write

jobs:
  create-requirements-review-issue:
    if: (github.event_name == 'pull_request' && github.event.pull_request.merged == true) || github.event_name == 'workflow_dispatch'
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Restore last scan state
        id: restore-state
        uses: actions/cache/restore@v4
        with:
          path: .requirements-scan-state
          key: requirements-scan-state-${{ github.repository }}

      - name: Get last scanned commit
        id: last-commit
        shell: bash
        run: |
          if [ -f ".requirements-scan-state/last-commit.txt" ]; then
            LAST_COMMIT=$(cat .requirements-scan-state/last-commit.txt)
            if [ -n "$LAST_COMMIT" ] && git rev-parse --verify "$LAST_COMMIT" >/dev/null 2>&1; then
              echo "last_commit=${LAST_COMMIT}" >> $GITHUB_OUTPUT
              echo "has_previous_state=true" >> $GITHUB_OUTPUT
              echo "Last scanned commit: ${LAST_COMMIT}"
            else
              echo "has_previous_state=false" >> $GITHUB_OUTPUT
              echo "Invalid or empty last scanned commit SHA"
            fi
          else
            echo "has_previous_state=false" >> $GITHUB_OUTPUT
            echo "No previous scan state found"
          fi

      - name: Determine scan parameters
        id: scan-params
        shell: bash
        run: |
          # Determine scan mode and commit range based on trigger type and inputs
          if [ "${{ github.event_name }}" == "workflow_dispatch" ]; then
            SCAN_MODE="${{ github.event.inputs.scan_mode }}"
            FORCE_CREATE="${{ github.event.inputs.force_create_issue }}"

            case "$SCAN_MODE" in
              "full")
                # Full scan from first commit
                FIRST_COMMIT=$(git rev-list --max-parents=0 HEAD)
                FROM_COMMIT="$FIRST_COMMIT"
                TO_COMMIT="HEAD"
                echo "Full scan mode: from $FIRST_COMMIT to HEAD"
                ;;
              "custom")
                FROM_COMMIT="${{ github.event.inputs.from_commit }}"
                TO_COMMIT="${{ github.event.inputs.to_commit }}"

                # Validate custom mode inputs
                if [ -z "$FROM_COMMIT" ] || [ -z "$TO_COMMIT" ]; then
                  echo "Error: Custom scan mode requires both from_commit and to_commit parameters"
                  echo "FROM_COMMIT: '$FROM_COMMIT'"
                  echo "TO_COMMIT: '$TO_COMMIT'"
                  exit 1
                fi

                echo "Custom scan mode: from $FROM_COMMIT to $TO_COMMIT"
                ;;
              "auto")
                # Auto mode: scan from last successful scan or use default behavior
                if [ "${{ steps['last-commit'].outputs['has_previous_state'] }}" == "true" ]; then
                  FROM_COMMIT="${{ steps['last-commit'].outputs.last_commit }}"
                  TO_COMMIT="HEAD"
                  echo "Auto scan mode: from last scan $FROM_COMMIT to HEAD"
                else
                  # No previous state, use default behavior based on event
                  FROM_COMMIT=""
                  TO_COMMIT=""
                  echo "Auto scan mode: no previous state, using default behavior"
                fi
                ;;
              *)
                echo "❌ Unknown scan_mode: $SCAN_MODE"
                exit 1
                ;;
            esac
          else
            # Automatic trigger (push/PR)
            SCAN_MODE="automatic"
            FORCE_CREATE="false"
            FROM_COMMIT=""
            TO_COMMIT=""
            echo "Automatic trigger mode"
          fi

          echo "scan_mode=${SCAN_MODE}" >> $GITHUB_OUTPUT
          echo "force_create_issue=${FORCE_CREATE}" >> $GITHUB_OUTPUT
          echo "from_commit=${FROM_COMMIT}" >> $GITHUB_OUTPUT
          echo "to_commit=${TO_COMMIT}" >> $GITHUB_OUTPUT

      - name: Analyze requirements changes
        id: requirements-analysis
        uses: ./.github/actions/requirements-review
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          event-name: ${{ github.event_name }}
          before-commit: ${{ github.event.before }}
          current-commit: ${{ github.sha }}
          pr-base-sha: ${{ github.event.pull_request.base.sha }}
          pr-head-sha: ${{ github.event.pull_request.head.sha }}
          scan-mode: ${{ steps['scan-params'].outputs.scan_mode }}
          manual-from-commit: ${{ steps['scan-params'].outputs.from_commit }}
          manual-to-commit: ${{ steps['scan-params'].outputs.to_commit }}
          force-create-issue: ${{ steps['scan-params'].outputs.force_create_issue }}

      - name: Create issue from template
        id: create-issue
        if: steps.requirements-analysis.outputs['has-changes'] == 'true' || steps['scan-params'].outputs.force_create_issue == 'true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const previousCommit = ${{ toJSON(steps['requirements-analysis'].outputs['previous-commit']) }};
            const currentCommit = ${{ toJSON(steps['requirements-analysis'].outputs['current-commit']) }};
            const changedFiles = ${{ toJSON(steps['requirements-analysis'].outputs['changed-files']) }};
            const diffContent = ${{ toJSON(steps['requirements-analysis'].outputs['diff-content']) }};

            // Validate required outputs
            if (!previousCommit || !currentCommit) {
              core.setFailed('Missing required commit information from requirements analysis');
              return;
            }

            const currentDate = new Date().toLocaleDateString('vi-VN');
            const currentDateTime = new Date().toLocaleString('vi-VN');

            const previousCommitLink = context.payload.repository.html_url + '/commit/' + previousCommit;
            const currentCommitLink = context.payload.repository.html_url + '/commit/' + currentCommit;

            const committer = context.payload.head_commit?.committer?.name ||
                             context.payload.pull_request?.user?.login ||
                             'Unknown';

            const issueTitle = '[REVIEW] Đánh giá thay đổi yêu cầu nghiệp vụ - ' + currentDate;

            // Create issue body with proper escaping
            const issueBody = [
              '## 📋 Thông tin thay đổi',
              '',
              '**Thời gian phát hiện:** ' + currentDateTime,
              '**Người commit:** ' + committer,
              '',
              '> 🤖 **@copilot** và **Jules**: Vui lòng đánh giá tác động của các thay đổi yêu cầu nghiệp vụ này.',
              '',
              '## Nội dung thay đổi',
              '',
              '**Commit trước:** `' + previousCommit.substring(0, 7) + '` ([Xem chi tiết](' + previousCommitLink + ') )',
              '**Commit sau:** `' + currentCommit.substring(0, 7) + '` ([Xem chi tiết](' + currentCommitLink + '))',
              '',
              '### Tệp tin bị thay đổi:',
              changedFiles,
              '',
              '### Chi tiết thay đổi:',
              '```diff',
              diffContent,
              '```',
              '',
              '## 🔍 Cần đánh giá',
              '',
              '### 1. Loại thay đổi',
              '- [ ] Tính năng mới',
              '- [ ] Sửa đổi tính năng cũ',
              '- [ ] Chưa phân loại',
              '',
              '### 2. Phân tích tác động',
              '**Thay đổi này sẽ ảnh hưởng đến những user story/epic nào?**',
              '',
              '_[Vui lòng điền thông tin]_',
              '',
              '### 3. Công việc cần thực hiện theo vai trò',
              '',
              '#### 👨‍🎨 Designer',
              '- [ ] Cần cập nhật UI/UX design',
              '- [ ] Cần cập nhật wireframe',
              '- [ ] Cần tạo prototype mới',
              '- [ ] Không cần thay đổi',
              '',
              '**Chi tiết công việc:**',
              '_[Vui lòng mô tả cụ thể]_',
              '',
              '#### 📊 System Analyst (SA)',
              '- [ ] Cần cập nhật tài liệu phân tích',
              '- [ ] Cần xem xét lại use case',
              '- [ ] Cần cập nhật business rules',
              '- [ ] Không cần thay đổi',
              '',
              '**Chi tiết công việc:**',
              '_[Vui lòng mô tả cụ thể]_',
              '',
              '#### 💻 Developer',
              '- [ ] Cần cập nhật code',
              '- [ ] Cần thay đổi API',
              '- [ ] Cần cập nhật database schema',
              '- [ ] Không cần thay đổi',
              '',
              '**Chi tiết công việc:**',
              '_[Vui lòng mô tả cụ thể]_',
              '',
              '#### 🧪 Tester',
              '- [ ] Cần cập nhật test case',
              '- [ ] Cần cập nhật test plan',
              '- [ ] Cần viết automation test mới',
              '- [ ] Không cần thay đổi',
              '',
              '**Chi tiết công việc:**',
              '_[Vui lòng mô tả cụ thể]_',
              '',
              '### 4. Mức độ ưu tiên',
              '- [ ] Thấp',
              '- [ ] Trung bình',
              '- [ ] Cao',
              '- [ ] Khẩn cấp',
              '',
              '### 5. Bước tiếp theo',
              '- [ ] Schedule meeting với stakeholders',
              '- [ ] Review và approve design changes',
              '- [ ] Cập nhật development plan',
              '- [ ] Thực hiện testing',
              '- [ ] Khác: _[Vui lòng mô tả]_',
              '',
              '### 6. Ghi chú bổ sung',
              '_[Thông tin bổ sung hoặc các rủi ro cần lưu ý]_',
              '',
              '---',
              '**🤖 Issue này được tạo tự động bởi GitHub Actions**',
              '',
              '**Assignees:**',
              '- Yêu cầu tới @terragon-labs:',
              '```',
              'Dựa trên thông tin github issues đã cung cấp, hãy tìm hiểu sâu vào phần nội dung mới cập nhật từ commit history, từ đó output ra được nội dung sau:',
              '',
              'Mô tả đầy đủ thay đổi.',
              'Đây là yêu cầu tính năng mới, hãy sửa tính năng cũ',
              'Thay đổi nay tác động đến các user story nào',
              'Thay đổi này cần các role tương ứng làm gì (SA, Dev, Tester, DevOps)',
              'Gửi comment vào github issue đang xử lý với đầy đủ nội dung đánh giá ở trên',
              '',
              'Tạo sub issue gắn với github issue hiện tại cho từng role các công việc cần làm, mô tả chi tiết nhất có thể việc cần làm cho từng role',
              '```'
            ].join('\n');

            const issue = await github.rest.issues.create({
              owner: context.repo.owner,
              repo: context.repo.repo,
              title: issueTitle,
              body: issueBody,
              labels: ['yêu-cầu-nghiệp-vụ', 'requirements-review', 'business-analysis', 'auto-generated']
            });

            console.log('Created issue: ' + issue.data.html_url);

            // Store issue number for subsequent steps
            core.setOutput('issue_number', issue.data.number);
            core.setOutput('issue_url', issue.data.html_url);

      - name: Assign to GitHub Copilot agent
        if: steps.requirements-analysis.outputs['has-changes'] == 'true' || steps['scan-params'].outputs.force_create_issue == 'true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const issueNumber = ${{ steps['create-issue'].outputs.issue_number }};

            // Assign issue to GitHub Copilot agent
            try {
              await github.rest.issues.addAssignees({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issueNumber,
                assignees: ['github-actions[bot]']
              });
              console.log('Assigned issue #' + issueNumber + ' to GitHub Copilot agent');
            } catch (error) {
              console.log('Could not assign to github-actions[bot], error:', error.message);
              // Try alternative assignee names
              const alternativeAssignees = ['copilot[bot]', 'github-copilot[bot]'];
              for (const assignee of alternativeAssignees) {
                try {
                  await github.rest.issues.addAssignees({
                    owner: context.repo.owner,
                    repo: context.repo.repo,
                    issue_number: issueNumber,
                    assignees: [assignee]
                  });
                  console.log('Successfully assigned issue #' + issueNumber + ' to ' + assignee);
                  break;
                } catch (altError) {
                  console.log('Could not assign to ' + assignee + ':', altError.message);
                }
              }
            }

      - name: Add terragon-labs assignment comment
        if: steps.requirements-analysis.outputs['has-changes'] == 'true' || steps['scan-params'].outputs.force_create_issue == 'true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const issueNumber = ${{ steps['create-issue'].outputs.issue_number }};

            if (!issueNumber) {
              console.log('Warning: issueNumber is undefined. Skipping comment creation.');
            } else {
              const assignmentComment = '@terragon-labs đọc nội dung giao việc ở cuối github issue và thực hiện công việc';

              await github.rest.issues.createComment({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issueNumber,
                body: assignmentComment
              });

              console.log('Added terragon-labs assignment comment to issue #' + issueNumber);
            }

      - name: Save scan state
        if: steps.requirements-analysis.outputs['has-changes'] == 'true' || github.event.inputs.force_create_issue == 'true'
        shell: bash
        run: |
          mkdir -p .requirements-scan-state
          echo "${{ steps['requirements-analysis'].outputs['current-commit'] }}" > .requirements-scan-state/last-commit.txt
          echo "$(date -u +"%Y-%m-%dT%H:%M:%SZ")" > .requirements-scan-state/last-scan-time.txt
          echo "${{ github.event_name }}" > .requirements-scan-state/last-trigger-type.txt
          echo "Saved scan state for commit: ${{ steps['requirements-analysis'].outputs['current-commit'] }}"

      - name: Cache scan state
        if: steps.requirements-analysis.outputs['has-changes'] == 'true'
        uses: actions/cache/save@v4
        with:
          path: .requirements-scan-state
          key: requirements-scan-state-${{ github.repository }}

      - name: Send Lark notification for requirements change
        if: steps.requirements-analysis.outputs['has-changes'] == 'true' || steps['scan-params'].outputs.force_create_issue == 'true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            // Send notification to Lark webhook directly
            const issueUrl = ${{ toJSON(steps['create-issue'].outputs.issue_url) }};
            const issueNumber = ${{ steps['create-issue'].outputs.issue_number }};
            const currentTime = new Date().toLocaleString('vi-VN', {timeZone: 'Asia/Ho_Chi_Minh'});

            const webhookUrl = process.env.LARK_CHAT_GROUP_NOTIFICATION;
            if (!webhookUrl) {
              console.log('LARK_CHAT_GROUP_NOTIFICATION not configured, skipping notification');
              return;
            }

            const projectCode = process.env.PROJECT_CODE || 'CMS';
            const eventType = '[OSP][' + projectCode + '] [Requirements Review] Phát hiện thay đổi yêu cầu nghiệp vụ';

            const content = [
              'Thời gian phát hiện: ' + currentTime,
              'Người commit: ' + (context.actor || 'Unknown'),
              'Issue Link: [#' + issueNumber + '](' + issueUrl + ')',
              'Repository: ' + context.repo.owner + '/' + context.repo.repo,
              '',
              '📝 **Hành động cần thực hiện:**',
              '• Đánh giá tác động thay đổi',
              '• Phân tích rủi ro',
              '• Cập nhật các vai trò liên quan',
              '• Review implementation plan'
            ].join('<br />');

            const payload = {
              msg_type: 'interactive',
              card: {
                config: { wide_screen_mode: true },
                elements: [
                  { tag: 'markdown', content: content }
                ],
                header: {
                  template: 'orange',
                  title: { content: eventType, tag: 'plain_text' }
                }
              }
            };

            const response = await fetch(webhookUrl, {
              method: 'POST',
              headers: { 'Content-Type': 'application/json' },
              body: JSON.stringify(payload)
            });

            if (response.ok) {
              console.log('Lark notification sent successfully');
            } else {
              console.log('Failed to send Lark notification:', response.status);
            }
        env:
          LARK_CHAT_GROUP_NOTIFICATION: ${{ secrets.LARK_CHAT_GROUP_NOTIFICATION }}
          PROJECT_CODE: ${{ vars.PROJECT_CODE }}

      - name: Add comment to PR (if applicable)
        if: github.event_name == 'pull_request' && steps.requirements-analysis.outputs['has-changes'] == 'true'
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            const comment = [
              '## 🔔 Thông báo: Phát hiện thay đổi yêu cầu nghiệp vụ',
              '',
              'Hệ thống đã phát hiện có thay đổi trong thư mục `docs/requirements`.',
              '',
              'Một issue đánh giá tác động đã được tạo tự động để review những thay đổi này:',
              '- 📋 **Issue:** Sẽ được tạo sau khi PR được merge',
              '- 🎯 **Mục đích:** Đánh giá tác động đến các user story/epic và phân công công việc cho các role',
              '- ⏰ **Thời gian:** Ngay sau khi merge',
              '',
              '**Lưu ý:** Vui lòng kiểm tra và xử lý issue review sau khi merge PR này.'
            ].join('\n');

            await github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: comment
            });
