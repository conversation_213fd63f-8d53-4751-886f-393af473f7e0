package vn.osp.common.domain.exceptions;

import vn.osp.common.domain.constants.ErrorCodesConst;

/**
 * Exception được ném khi business rule bị vi phạm.
 * Sử dụng cho các lỗi logic nghiệp vụ như validation rules, business constraints.
 */
public class BusinessRuleException extends DomainException {

    /**
     * Khởi tạo business rule exception với rule name và message.
     * Error code sẽ được tự động format thành "BUSINESS_RULE_{RULE_NAME}".
     *
     * <pre>
     * Example:
     * throw new BusinessRuleException("MIN_AGE", "User must be at least 18 years old", 18);
     * // Error code: BUSINESS_RULE_MIN_AGE
     * </pre>
     *
     * @param rule       tên rule bị vi phạm (sẽ được convert thành uppercase)
     * @param message    thông điệp mô tả chi tiết lỗi
     * @param parameters tham số bổ sung
     */
    public BusinessRuleException(String rule, String message, Object... parameters) {
        super(ErrorCodesConst.businessRule(rule), message, parameters);
    }

    /**
     * Khởi tạo business rule exception với nguyên nhân gốc (cause).
     *
     * @param rule       tên rule bị vi phạm
     * @param message    thông điệp mô tả chi tiết lỗi
     * @param cause      exception gây ra lỗi này
     * @param parameters tham số bổ sung
     */
    public BusinessRuleException(String rule, String message, Throwable cause, Object... parameters) {
        super(ErrorCodesConst.businessRule(rule), message, cause, parameters);
    }
}