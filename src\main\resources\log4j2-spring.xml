<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="WARN">
    <Appenders>
        <Console name="Console" target="SYSTEM_OUT">
            <PatternLayout
                    pattern="%d{HH:mm:ss.SSS} [%t] %-5level %logger{36} trace_id: %X{trace_id} span_id: %X{span_id} - %msg%n"/>
        </Console>
        <OpenTelemetry name="OpenTelemetryAppender"
                       captureMapMessageAttributes="true"
                       captureMarkerAttribute="true"
                       captureContextDataAttributes=""
        />
    </Appenders>
    <Loggers>
        <Root level="INFO">
            <AppenderRef ref="OpenTelemetryAppender"/>
            <AppenderRef ref="Console"/>
        </Root>
    </Loggers>
</Configuration>