package vn.osp.common.domain.exceptions;

import vn.osp.common.domain.constants.ErrorCodesConst;

/**
 * Exception được ném khi user đã authenticate nhưng không có quyền thực hiện action.
 * Tương ứng với HTTP 403 Forbidden status code.
 * <p>
 * Kh<PERSON>c với UnauthorizedDomainException - đ<PERSON>y là về authorization, không phải authentication.
 */
public class ForbiddenDomainException extends DomainException {

    /**
     * Khởi tạo forbidden exception với resource và lý do.
     *
     * <pre>
     * throw new ForbiddenDomainException("system user", "Only administrators can modify system users");
     * throw new ForbiddenDomainException("financial reports", "Insufficient permissions");
     * </pre>
     *
     * @param resource resource bị từ chối truy cập
     * @param reason   lý do từ chối truy cập
     */
    public ForbiddenDomainException(String resource, String reason) {
        super(
                ErrorCodesConst.FORBIDDEN_ACCESS,
                "Access to %s is forbidden: %s.".formatted(resource, reason),
                resource,
                reason
        );
    }

    /**
     * Khởi tạo với role-based access denial.
     *
     * @param resource      resource cần truy cập
     * @param requiredRoles các role cần thiết
     * @param currentRole   role hiện tại của user
     */
    public ForbiddenDomainException(String resource, String[] requiredRoles, String currentRole) {
        super(
                ErrorCodesConst.INSUFFICIENT_ROLE,
                formatRoleDeniedMessage(resource, requiredRoles, currentRole),
                resource,
                requiredRoles,
                currentRole
        );
    }

    private static String formatRoleDeniedMessage(String resource, String[] requiredRoles, String currentRole) {
        String roles = (requiredRoles == null || requiredRoles.length == 0)
                ? "N/A"
                : String.join(", ", requiredRoles);
        String curr = (currentRole == null || currentRole.isBlank()) ? "N/A" : currentRole;
        return "Access to %s requires one of these roles: [%s]. Current role: %s."
                .formatted(resource, roles, curr);
    }
    /**
     * Khởi tạo với ownership-based access denial.
     *
     * @param resource        resource cần truy cập
     * @param resourceOwnerId ID của chủ sở hữu resource
     * @param requesterId     ID của user đang request
     * @return ForbiddenDomainException
     */
    public static ForbiddenDomainException ownershipRequired(String resource, Object resourceOwnerId, Object requesterId) {
        return new ForbiddenDomainException(
                ErrorCodesConst.OWNERSHIP_REQUIRED,
                "Access to %s denied. Only the owner (ID: %s) can access this resource. Requester ID: %s."
                        .formatted(resource, resourceOwnerId, requesterId),
                resource,
                resourceOwnerId,
                requesterId
        );
    }

    /**
     * Internal constructor với custom error code.
     *
     * @param errorCode  custom error code
     * @param message    thông điệp lỗi
     * @param parameters các tham số
     */
    private ForbiddenDomainException(String errorCode, String message, Object... parameters) {
        super(errorCode, message, parameters);
    }
}