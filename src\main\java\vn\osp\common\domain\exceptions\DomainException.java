package vn.osp.common.domain.exceptions;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Base exception class cho tất cả domain-specific exceptions.
 * Cung cấp error code và parameters cho structured error handling.
 */
@Getter
public abstract class DomainException extends RuntimeException {

    /**
     * Mã lỗi duy nhất để xác định loại lỗi cụ thể.
     */
    private final String errorCode;

    /**
     * C<PERSON>c tham số bổ sung để format error message hoặc logging.
     */
    private final List<Object> parameters;

    /**
     * Khởi tạo domain exception với error code, message và parameters.
     *
     * @param errorCode  mã lỗi duy nhất
     * @param message    thông điệp lỗi
     * @param parameters tham số bổ sung
     */
    protected DomainException(String errorCode, String message, Object... parameters) {
        super(message);
        this.errorCode = errorCode;
        this.parameters = parameters != null ? List.copyOf(Arrays.asList(parameters)) : Collections.emptyList();
    }

    /**
     * Khởi tạo domain exception với inner exception.
     *
     * @param errorCode  mã lỗi duy nhất
     * @param message    thông điệp lỗi
     * @param cause      exception gốc
     * @param parameters tham số bổ sung
     */
    protected DomainException(String errorCode, String message, Throwable cause, Object... parameters) {
        super(message, cause);
        this.errorCode = errorCode;
        this.parameters = parameters != null ? List.copyOf(Arrays.asList(parameters)) : Collections.emptyList();
    }

    /**
     * Override toString để bao gồm error code trong output.
     */
    @Override
    public String toString() {
        return "[" + errorCode + "] " + super.toString();
    }
}