package vn.osp.common.application.exceptions;

import jakarta.annotation.Nullable;
import lombok.Getter;
import vn.osp.common.application.constants.ErrorCodesConst;

import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Exception được ném khi user chưa đượ<PERSON> authenticate (401 Unauthorized).
 * Đây là Application/Infrastructure concern, không phải Domain concern.
 * Thường được ném bởi Authentication Middleware hoặc Authorization services.
 * HTTP Status: 401 Unauthorized
 */
@Getter
public class UnauthorizedException extends RuntimeException {

    /**
     * Error code để phân loại lỗi authentication.
     */
    private final String errorCode;

    /**
     * Các parameters bổ sung cho exception.
     */
    private final List<Object> parameters;

    /**
     * Khởi tạo với thông điệp mặc định.
     *
     * <pre>{@code
     * throw new UnauthorizedException();
     * }</pre>
     */
    public UnauthorizedException() {
        super("Authentication required. Please provide valid credentials.");
        this.errorCode = ErrorCodesConst.AUTHENTICATION_REQUIRED;
        this.parameters = Collections.emptyList();
    }

    /**
     * Khởi tạo với custom message.
     *
     * @param message Thông điệp lỗi tùy chỉnh
     * <pre>{@code
     * throw new UnauthorizedException("Token has expired");
     * }</pre>
     */
    public UnauthorizedException(String message) {
        super(message);
        this.errorCode = ErrorCodesConst.AUTHENTICATION_REQUIRED;
        this.parameters = List.of(message);
    }

    /**
     * Khởi tạo với inner exception.
     *
     * @param message Thông điệp lỗi
     * @param cause   Exception gốc
     */
    public UnauthorizedException(String message, Throwable cause) {
        super(message, cause);
        this.errorCode = ErrorCodesConst.AUTHENTICATION_REQUIRED;
        this.parameters = List.of(message);
    }

    /**
     * Internal constructor với custom error code.
     */
    private UnauthorizedException(String errorCode, String message, Object... parameters) {
        super(message);
        this.errorCode = errorCode;
        this.parameters = parameters != null ? List.copyOf(Arrays.asList(parameters)) : Collections.emptyList();
    }

    // ======================
    // Static Factory Methods
    // ======================

    /**
     * Token đã hết hạn.
     *
     * @param tokenType Loại token (JWT, Refresh Token, etc.)
     * @param expiredAt Thời điểm hết hạn
     */
    public static UnauthorizedException tokenExpired(String tokenType, @Nullable OffsetDateTime expiredAt) {
        String expiredInfo = expiredAt != null
                ? " (expired at: " + expiredAt.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + ")"
                : "N/A";
        return new UnauthorizedException(
                ErrorCodesConst.TOKEN_EXPIRED,
                tokenType + " has expired" + expiredInfo + ". Please login again.",
                tokenType,
                expiredAt
        );
    }

    /**
     * Token không hợp lệ hoặc bị corrupt.
     *
     * @param tokenType Loại token
     * @param reason    Lý do không hợp lệ (optional)
     */
    public static UnauthorizedException invalidToken(String tokenType, @Nullable String reason) {
        String reasonInfo = (reason != null && !reason.isBlank()) ? " Reason: " + reason : "N/A";
        return new UnauthorizedException(
                ErrorCodesConst.TOKEN_INVALID,
                tokenType + " is invalid or malformed." + reasonInfo,
                tokenType,
                reason != null ? reason : "N/A"
        );
    }

    /**
     * Credentials không đúng (username/password sai).
     *
     * @param attemptCount Số lần thử sai (optional)
     */
    public static UnauthorizedException invalidCredentials(@Nullable Integer attemptCount) {
        String attemptInfo = (attemptCount != null) ? " Attempt #" + attemptCount + "." : "N/A";
        return new UnauthorizedException(
                ErrorCodesConst.INVALID_CREDENTIALS,
                "Invalid username or password." + attemptInfo,
                attemptCount != null ? attemptCount : 0
        );
    }

    /**
     * Account bị khóa do quá nhiều lần đăng nhập sai.
     *
     * @param lockoutEnd Thời điểm mở khóa
     */
    public static UnauthorizedException accountLocked(OffsetDateTime lockoutEnd) {
        return new UnauthorizedException(
                ErrorCodesConst.ACCOUNT_LOCKED,
                "Account is temporarily locked due to multiple failed login attempts. Try again after "
                        + lockoutEnd.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + ".",
                lockoutEnd
        );
    }

    /**
     * Session đã hết hạn, cần login lại.
     *
     * @param sessionId ID của session (optional)
     */
    public static UnauthorizedException sessionExpired(@Nullable String sessionId) {
        return new UnauthorizedException(
                ErrorCodesConst.SESSION_EXPIRED,
                "Your session has expired. Please login again.",
                sessionId != null ? sessionId : "N/A"
        );
    }
}