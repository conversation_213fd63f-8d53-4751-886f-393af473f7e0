package vn.osp.common.api.exceptionhandlers;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import jakarta.persistence.OptimisticLockException;
import jakarta.validation.ConstraintViolationException;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.context.MessageSource;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.core.env.Environment;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.http.ProblemDetail;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import vn.osp.common.application.exceptions.UnauthorizedException;
import vn.osp.common.domain.exceptions.*;
import vn.osp.common.infrastructure.constants.ErrorCodesConst;
import vn.osp.common.infrastructure.exceptions.ExternalServiceException;

import java.net.URI;
import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.NoSuchElementException;
import java.util.Set;

@Slf4j
@RestControllerAdvice
@RequiredArgsConstructor
public class GlobalExceptionHandler {
    private final Environment environment;
    private final MessageSource messageSource;

    private static final String EXT_ERRORS = "errors";
    private static final String EXT_ERROR_CODE = "errorCode";
    private static final String EXT_CORRELATION_ID = "correlationId";
    private static final String EXT_TIMESTAMP = "timestamp";
    private static final String EXT_PARAMETERS = "parameters";

    @ExceptionHandler(EntityNotFoundException.class)
    public ProblemDetail handleEntityNotFound(EntityNotFoundException ex) {
        logging(ex);
        recordException(ex);
        ProblemDetail problem = ProblemDetail.forStatus(HttpStatus.NOT_FOUND);
        problem.setTitle("Entity Not Found");
        problem.setDetail(ex.getMessage());
        problem.setType(URI.create("https://tools.ietf.org/html/rfc7231#section-6.5.4"));

        problem.setProperty(EXT_ERROR_CODE, ex.getErrorCode());
        problem.setProperty(EXT_PARAMETERS, ex.getParameters());

        enrichCommon(problem);
        return problem;
    }

    @ExceptionHandler(UnauthorizedException.class)
    public ProblemDetail handleUnauthorized(UnauthorizedException ex) {
        logging(ex);
        recordException(ex);
        ProblemDetail problem = ProblemDetail.forStatus(HttpStatus.UNAUTHORIZED);
        problem.setTitle("Authentication Required");
        problem.setDetail(ex.getMessage());
        problem.setType(URI.create("https://tools.ietf.org/html/rfc7235#section-3.1"));

        problem.setProperty(EXT_ERROR_CODE, ex.getErrorCode());
        problem.setProperty(EXT_PARAMETERS, ex.getParameters());

        enrichCommon(problem);
        return problem;
    }

    @ExceptionHandler(ForbiddenDomainException.class)
    public ProblemDetail handleForbidden(ForbiddenDomainException ex) {
        logging(ex);
        recordException(ex);
        ProblemDetail problem = ProblemDetail.forStatus(HttpStatus.FORBIDDEN);
        problem.setTitle("Forbidden Access");
        problem.setDetail(ex.getMessage());
        problem.setType(URI.create("https://tools.ietf.org/html/rfc7231#section-6.5.3"));

        problem.setProperty(EXT_ERROR_CODE, ex.getErrorCode());
        problem.setProperty(EXT_PARAMETERS, ex.getParameters());

        enrichCommon(problem);
        return problem;
    }

    @ExceptionHandler(BusinessRuleException.class)
    public ProblemDetail handleBusinessRule(BusinessRuleException ex) {
        logging(ex);
        recordException(ex);
        ProblemDetail problem = ProblemDetail.forStatus(HttpStatus.UNPROCESSABLE_ENTITY);
        problem.setTitle("Business Rule Violation");
        problem.setDetail(ex.getMessage());
        problem.setType(URI.create("https://tools.ietf.org/html/rfc4918#section-11.2"));

        problem.setProperty(EXT_ERROR_CODE, ex.getErrorCode());
        problem.setProperty(EXT_PARAMETERS, ex.getParameters());

        enrichCommon(problem);
        return problem;
    }

    @ExceptionHandler(ConcurrencyException.class)
    public ProblemDetail handleConcurrency(ConcurrencyException ex) {
        logging(ex);
        recordException(ex);
        ProblemDetail problem = ProblemDetail.forStatus(HttpStatus.CONFLICT);
        problem.setTitle("Concurrency Conflict");
        problem.setDetail(ex.getMessage());
        problem.setType(URI.create("https://tools.ietf.org/html/rfc7231#section-6.5.8"));

        problem.setProperty(EXT_ERROR_CODE, ex.getErrorCode());
        problem.setProperty(EXT_PARAMETERS, ex.getParameters());

        enrichCommon(problem);
        return problem;
    }

    @ExceptionHandler(DuplicateEntityException.class)
    public ProblemDetail handleDuplicate(DuplicateEntityException ex) {
        logging(ex);
        recordException(ex);
        ProblemDetail problem = ProblemDetail.forStatus(HttpStatus.CONFLICT);
        problem.setTitle("Duplicate Entity");
        problem.setDetail(ex.getMessage());
        problem.setType(URI.create("https://tools.ietf.org/html/rfc7231#section-6.5.8"));

        problem.setProperty(EXT_ERROR_CODE, ex.getErrorCode());
        problem.setProperty(EXT_PARAMETERS, ex.getParameters());

        enrichCommon(problem);
        return problem;
    }

    @ExceptionHandler(InvalidOperationDomainException.class)
    public ProblemDetail handleInvalidOperationDomain(InvalidOperationDomainException ex) {
        logging(ex);
        recordException(ex);
        ProblemDetail problem = ProblemDetail.forStatus(HttpStatus.UNPROCESSABLE_ENTITY);
        problem.setTitle("Invalid Operation");
        problem.setDetail(ex.getMessage());
        problem.setType(URI.create("https://tools.ietf.org/html/rfc4918#section-11.2"));

        problem.setProperty(EXT_ERROR_CODE, ex.getErrorCode());
        problem.setProperty(EXT_PARAMETERS, ex.getParameters());

        enrichCommon(problem);
        return problem;
    }

    @ExceptionHandler(ExternalServiceException.class)
    public ProblemDetail handleExternalService(ExternalServiceException ex) {
        logging(ex);
        recordException(ex);
        HttpStatus statusCode = switch (ex.getErrorCode()) {
            case ErrorCodesConst.EXTERNAL_SERVICE_UNAVAILABLE -> HttpStatus.SERVICE_UNAVAILABLE;
            case ErrorCodesConst.EXTERNAL_SERVICE_TIMEOUT -> HttpStatus.GATEWAY_TIMEOUT;
            case ErrorCodesConst.EXTERNAL_SERVICE_RATE_LIMITED -> HttpStatus.TOO_MANY_REQUESTS;
            default -> HttpStatus.BAD_GATEWAY;
        };
        ProblemDetail problem = ProblemDetail.forStatus(statusCode);
        problem.setTitle("External Service Error");
        problem.setDetail(ex.getMessage());

        URI type = switch (statusCode) {
            case HttpStatus.BAD_GATEWAY -> URI.create("https://tools.ietf.org/html/rfc7231#section-6.6.3");
            case HttpStatus.SERVICE_UNAVAILABLE -> URI.create("https://tools.ietf.org/html/rfc7231#section-6.6.4");
            case HttpStatus.GATEWAY_TIMEOUT -> URI.create("https://tools.ietf.org/html/rfc7231#section-6.6.5");
            case HttpStatus.TOO_MANY_REQUESTS -> URI.create("https://tools.ietf.org/html/rfc6585#section-4");
            default -> URI.create("https://tools.ietf.org/html/rfc7231#section-6.6.3");
        };
        problem.setType(type);

        problem.setProperty(EXT_ERROR_CODE, ex.getErrorCode());
        problem.setProperty(EXT_PARAMETERS, ex.getParameters());

        enrichCommon(problem);
        return problem;
    }

    // Handle validation exceptions (FluentValidation ≈ ConstraintViolationException)
    @ExceptionHandler(ConstraintViolationException.class)
    public ProblemDetail handleValidation(ConstraintViolationException ex) {
        logging(ex);
        recordException(ex);
        ProblemDetail problem = ProblemDetail.forStatus(HttpStatus.BAD_REQUEST);
        problem.setTitle(messageSource.getMessage("V0001_FluentValidationResponseTitle", null, LocaleContextHolder.getLocale()));
        problem.setDetail(ex.getMessage());
        problem.setType(URI.create(ex.getClass().getSimpleName()));

        problem.setProperty(EXT_ERRORS, ex.getConstraintViolations().stream()
                .map(v -> {
                    var m = new HashMap<String, Object>();
                    m.put("property", v.getPropertyPath() != null ? v.getPropertyPath().toString() : null);
                    m.put("message", v.getMessage());
                    m.put("invalidValue", v.getInvalidValue());
                    return m;
                }).toList());

        enrichCommon(problem);
        return problem;
    }

    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ProblemDetail handleMethodArgumentNotValid(MethodArgumentNotValidException ex) {
        logging(ex);
        recordException(ex);
        ProblemDetail problem = ProblemDetail.forStatus(HttpStatus.BAD_REQUEST);
        problem.setTitle(messageSource.getMessage("V0001_FluentValidationResponseTitle", null, LocaleContextHolder.getLocale()));
        problem.setDetail(ex.getMessage());
        problem.setType(URI.create(ex.getClass().getSimpleName()));

        problem.setProperty(EXT_ERRORS, ex.getBindingResult().getFieldErrors().stream()
                .map(err -> {
                    var m = new HashMap<String, Object>();
                    m.put("field", err.getField());
                    m.put("message", err.getDefaultMessage());
                    m.put("rejectedValue", err.getRejectedValue());
                    return m;
                })
                .toList());

        enrichCommon(problem);
        return problem;
    }

    // Handle KeyNotFoundException -> NoSuchElementException in Java
    @ExceptionHandler(NoSuchElementException.class)
    public ProblemDetail handleKeyNotFound(NoSuchElementException ex) {
        logging(ex);
        recordException(ex);
        ProblemDetail problem = ProblemDetail.forStatus(HttpStatus.NOT_FOUND);
        problem.setTitle("Key not found");
        problem.setDetail(ex.getMessage());
        problem.setType(URI.create(ex.getClass().getSimpleName()));

        enrichCommon(problem);
        return problem;
    }

    // Handle DbUpdateConcurrencyException -> OptimisticLockException
    @ExceptionHandler(OptimisticLockException.class)
    public ProblemDetail handleOptimisticLock(OptimisticLockException ex) {
        logging(ex);
        recordException(ex);
        ProblemDetail problem = ProblemDetail.forStatus(HttpStatus.CONFLICT);
        problem.setTitle(messageSource.getMessage("V0002_OptimisticLockExceptionMessage", null, LocaleContextHolder.getLocale()));
        problem.setType(URI.create(ex.getClass().getSimpleName()));

        enrichCommon(problem);
        return problem;
    }

    // Handle DbUpdateException -> DataIntegrityViolationException
    @ExceptionHandler(DataIntegrityViolationException.class)
    public ProblemDetail handleDbUpdate(DataIntegrityViolationException ex) {
        logging(ex);
        recordException(ex);
        ProblemDetail problem = ProblemDetail.forStatus(HttpStatus.BAD_REQUEST);
        problem.setTitle(messageSource.getMessage("V0004_DbUpdateExceptionMessage", null, LocaleContextHolder.getLocale()));
        problem.setDetail(ex.getMessage());
        problem.setType(URI.create(ex.getClass().getSimpleName()));

        enrichCommon(problem);
        return problem;
    }

    // Fallback handler
    @ExceptionHandler(Exception.class)
    public ProblemDetail handleGeneric(Exception ex) {
        logging(ex);
        recordException(ex);
        ProblemDetail problem = ProblemDetail.forStatus(HttpStatus.INTERNAL_SERVER_ERROR);
        problem.setTitle(messageSource.getMessage("V0003_UnhandledException", null, LocaleContextHolder.getLocale()));
        problem.setDetail(ex.getMessage());
        problem.setType(URI.create(ex.getClass().getSimpleName()));

        enrichCommon(problem);
        return problem;
    }

    private void enrichCommon(ProblemDetail problem) {
        if (!isDevelopment()) {
            problem.setDetail(null); // hide sensitive details
        }

        String correlationId = MDC.get("traceId");
        problem.setProperty(EXT_CORRELATION_ID, correlationId);
        problem.setProperty(EXT_TIMESTAMP, OffsetDateTime.now());
    }

    private void logging(Exception ex) {
        log.error("Exception: ", ex);
    }

    private void recordException(Exception ex) {
        Span span = Span.current();
        if (span != null && span.getSpanContext().isValid()) {
            span.recordException(ex);
            span.setStatus(StatusCode.ERROR, ex.getMessage());
        }
    }

    private boolean isDevelopment() {
        return Set.of(environment.getActiveProfiles()).contains("dev");
    }
}
