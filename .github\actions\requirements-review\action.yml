name: 'Requirements Review Analysis'
description: 'Analyzes changes in requirements documents and extracts diff information'

inputs:
  github-token:
    description: 'GitHub token for API access'
    required: true
  event-name:
    description: 'GitHub event name (push, pull_request, or workflow_dispatch)'
    required: true
  before-commit:
    description: 'Previous commit SHA for push events'
    required: false
  current-commit:
    description: 'Current commit SHA'
    required: true
  pr-base-sha:
    description: 'PR base commit SHA for pull request events'
    required: false
  pr-head-sha:
    description: 'PR head commit SHA for pull request events'
    required: false
  scan-mode:
    description: 'Scan mode (automatic, auto, full, custom)'
    required: false
    default: 'automatic'
  manual-from-commit:
    description: 'Manual scan from commit SHA'
    required: false
  manual-to-commit:
    description: 'Manual scan to commit SHA'
    required: false
  force-create-issue:
    description: 'Force create issue even if no changes'
    required: false
    default: 'false'

outputs:
  has-changes:
    description: 'Whether there are changes in docs/requirements'
    value: ${{ steps.analyze-changes.outputs.has_changes }}
  changed-files:
    description: 'List of changed files in docs/requirements'
    value: ${{ steps.analyze-changes.outputs.changed_files }}
  diff-content:
    description: 'Diff content for changed files'
    value: ${{ steps.analyze-changes.outputs.diff_content }}
  previous-commit:
    description: 'Previous commit SHA used for comparison'
    value: ${{ steps.analyze-changes.outputs.previous_commit }}
  current-commit:
    description: 'Current commit SHA used for comparison'
    value: ${{ steps.analyze-changes.outputs.current_commit }}

runs:
  using: 'composite'
  steps:
    - name: Analyze requirements changes
      id: analyze-changes
      shell: bash
      run: |
        # Determine commit range based on event type and scan mode
        SCAN_MODE="${{ inputs['scan-mode'] }}"
        FORCE_CREATE="${{ inputs['force-create-issue'] }}"

        echo "Scan mode: $SCAN_MODE"
        echo "Force create issue: $FORCE_CREATE"

        if [ "${{ inputs['event-name'] }}" == "workflow_dispatch" ]; then
          # Manual trigger - use provided commit range
          if [ "${SCAN_MODE}" == "auto" ]; then
            PREVIOUS_COMMIT="${{ inputs['manual-from-commit'] }}"
            CURRENT_COMMIT="${{ inputs['manual-to-commit'] }}"
            echo "Auto mode: using provided commit range (manual-from-commit may be empty)"
          elif [ -n "${{ inputs['manual-from-commit'] }}" ] && [ -n "${{ inputs['manual-to-commit'] }}" ]; then
            PREVIOUS_COMMIT="${{ inputs['manual-from-commit'] }}"
            CURRENT_COMMIT="${{ inputs['manual-to-commit'] }}"
            echo "Manual trigger: using provided commit range"
          else
            echo "Error: Manual trigger requires from and to commits unless in auto mode"
            exit 1
          fi
        elif [ "${{ inputs['event-name'] }}" == "push" ]; then
          PREVIOUS_COMMIT="${{ inputs['before-commit'] }}"
          CURRENT_COMMIT="${{ inputs['current-commit'] }}"
          echo "Push trigger: using event commits"
        else
          # For merged PR
          PREVIOUS_COMMIT="${{ inputs['pr-base-sha'] }}"
          CURRENT_COMMIT="${{ inputs['pr-head-sha'] }}"
          echo "PR trigger: using PR commits"
        fi
        
        echo "previous_commit=${PREVIOUS_COMMIT}" >> $GITHUB_OUTPUT
        echo "current_commit=${CURRENT_COMMIT}" >> $GITHUB_OUTPUT

        # Get list of changed files in docs/requirements
        if [ -z "$PREVIOUS_COMMIT" ]; then
          echo "PREVIOUS_COMMIT is empty. No previous state to diff against."
          echo "No changes in docs/requirements directory (no previous commit)"
          echo "has_changes=false" >> $GITHUB_OUTPUT
          # Handle force create logic separately
          if [ "$FORCE_CREATE" == "true" ]; then
            echo "Force create is enabled, proceeding anyway"
            echo "has_changes=true" >> $GITHUB_OUTPUT
            # Create a placeholder for changed files
            echo 'changed_files<<EOF' >> $GITHUB_OUTPUT
            echo "- Không có thay đổi file (manual trigger với force create)" >> $GITHUB_OUTPUT
            echo 'EOF' >> $GITHUB_OUTPUT
            # Create a placeholder diff
            echo 'diff_content<<EOF' >> $GITHUB_OUTPUT
            echo "Không có thay đổi nội dung (manual trigger với force create)" >> $GITHUB_OUTPUT
            echo 'EOF' >> $GITHUB_OUTPUT
            exit 0
          else
            echo "has_changes=false" >> $GITHUB_OUTPUT
            exit 0
          fi
        fi
        CHANGED_FILES=$(git diff --name-only ${PREVIOUS_COMMIT}..${CURRENT_COMMIT} -- docs/requirements/)

        if [ -z "$CHANGED_FILES" ]; then
          echo "No changes in docs/requirements directory"
          echo "has_changes=false" >> $GITHUB_OUTPUT
          # Handle force create logic separately
          if [ "$FORCE_CREATE" == "true" ]; then
            echo "Force create is enabled, proceeding anyway"
            echo "has_changes=true" >> $GITHUB_OUTPUT
            # Create a placeholder for changed files
            echo 'changed_files<<EOF' >> $GITHUB_OUTPUT
            echo "- Không có thay đổi file (manual trigger với force create)" >> $GITHUB_OUTPUT
            echo 'EOF' >> $GITHUB_OUTPUT
            # Create a placeholder diff
            echo 'diff_content<<EOF' >> $GITHUB_OUTPUT
            echo "Không có thay đổi nội dung (manual trigger với force create)" >> $GITHUB_OUTPUT
            echo 'EOF' >> $GITHUB_OUTPUT
            exit 0
          else
            echo "has_changes=false" >> $GITHUB_OUTPUT
            exit 0
          fi
        fi

        echo "has_changes=true" >> $GITHUB_OUTPUT
        
        # Format changed files for display
        FORMATTED_FILES=""
        while IFS= read -r file; do
          if [ -n "$file" ]; then
            FORMATTED_FILES="${FORMATTED_FILES}- \`${file}\`\n"
          fi
        done <<< "$CHANGED_FILES"
        
        # Save multiline output
        {
          echo 'changed_files<<EOF'
          echo -e "$FORMATTED_FILES"
          echo 'EOF'
        } >> $GITHUB_OUTPUT
        
        # Get diff for docs/requirements directory (removed head -500 limit)
        DIFF_CONTENT=$(git diff ${PREVIOUS_COMMIT}..${CURRENT_COMMIT} -- docs/requirements/)

        # Truncate if too long and add notice with link to full diff
        if [ ${#DIFF_CONTENT} -gt 10000 ]; then
          REPO_URL="${{ github.server_url }}/${{ github.repository }}"
          COMPARE_URL="${REPO_URL}/compare/${PREVIOUS_COMMIT}...${CURRENT_COMMIT}#files_bucket"
          TRUNCATE_MSG="**[Nội dung diff quá dài, đã bị cắt ngắn. Xem chi tiết tại: ${COMPARE_URL}]**"
          DIFF_CONTENT=$(printf "%s\n\n%s" "${DIFF_CONTENT:0:10000}" "$TRUNCATE_MSG")
        fi

        {
          echo 'diff_content<<EOF'
          echo "$DIFF_CONTENT"
          echo 'EOF'
        } >> $GITHUB_OUTPUT
