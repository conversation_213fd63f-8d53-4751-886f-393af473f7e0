package vn.osp.common.domain.exceptions;

import vn.osp.common.domain.constants.ErrorCodesConst;

/**
 * Exception được ném khi operation không hợp lệ trong trạng thái hiện tại của entity.
 * Khác với BusinessRuleException - đây là về state transitions, không phải business rules.
 * Tương ứng với HTTP 422 Unprocessable Entity status code.
 */
public class InvalidOperationDomainException extends DomainException {

    /**
     * Khởi tạo invalid operation exception với entity và operation.
     *
     * <pre>
     * throw new InvalidOperationDomainException("Order", "Cancel", "Shipped");
     * throw new InvalidOperationDomainException("User", "Delete", "Active");
     * </pre>
     *
     * @param entityName   tên entity
     * @param operation    operation bị reject
     * @param currentState trạng thái hiện tại
     */
    public InvalidOperationDomainException(String entityName, String operation, String currentState) {
        super(
                ErrorCodesConst.INVALID_OPERATION_STATE,
                "Cannot %s %s in current state '%s'.".formatted(operation, entityName, currentState),
                entityName,
                operation,
                currentState
        );
    }

    /**
     * Khởi tạo với custom message và optional parameters.
     *
     * <pre>
     * throw new InvalidOperationDomainException("Cannot process payment for cancelled order", "Order", "ProcessPayment");
     * </pre>
     *
     * @param message    thông điệp mô tả chi tiết
     * @param parameters các parameters bổ sung
     */
    public InvalidOperationDomainException(String message, Object... parameters) {
        super(ErrorCodesConst.INVALID_OPERATION_STATE, message, parameters);
    }

    /**
     * Khởi tạo với workflow state context.
     *
     * @param entityName     tên entity
     * @param currentState   trạng thái hiện tại
     * @param requiredStates các trạng thái hợp lệ cho operation
     * @param operation      operation bị reject
     * @return InvalidOperationDomainException
     */
    public static InvalidOperationDomainException invalidWorkflowState(
            String entityName,
            String currentState,
            String[] requiredStates,
            String operation) {
        String validStates = (requiredStates == null || requiredStates.length == 0)
                ? "N/A"
                : String.join(", ", requiredStates);

        return new InvalidOperationDomainException(
                ErrorCodesConst.INVALID_WORKFLOW_STATE,
                "Cannot %s %s in state '%s'. Valid states: [%s]"
                        .formatted(operation, entityName, currentState, validStates),
                entityName,
                operation,
                currentState,
                validStates);
    }

    /**
     * Internal constructor với custom error code.
     */
    private InvalidOperationDomainException(String errorCode, String message, Object... parameters) {
        super(errorCode, message, parameters);
    }
}