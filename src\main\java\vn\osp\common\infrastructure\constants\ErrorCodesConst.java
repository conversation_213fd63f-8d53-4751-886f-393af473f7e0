package vn.osp.common.infrastructure.constants;

/**
 * Chứa các error codes cho tất cả Infrastructure exceptions.
 * Gi<PERSON><PERSON> chuẩn hóa error handling cho các vấn đề về Configuration và External Services.
 */
public final class ErrorCodesConst {

    // Ngăn khởi tạo
    private ErrorCodesConst() {
    }

    // ===============================
    // External Service Errors
    // ===============================

    /**
     * Lỗi chung từ external service.
     * HTTP Status: 502 Bad Gateway
     */
    public static final String EXTERNAL_SERVICE_ERROR = "EXTERNAL_SERVICE_ERROR";

    /**
     * External service temporarily unavailable.
     * HTTP Status: 503 Service Unavailable
     */
    public static final String EXTERNAL_SERVICE_UNAVAILABLE = "EXTERNAL_SERVICE_UNAVAILABLE";

    /**
     * External service request timeout.
     * HTTP Status: 504 Gateway Timeout
     */
    public static final String EXTERNAL_SERVICE_TIMEOUT = "EXTERNAL_SERVICE_TIMEOUT";

    /**
     * External service rate limit exceeded.
     * HTTP Status: 429 Too Many Requests
     */
    public static final String EXTERNAL_SERVICE_RATE_LIMITED = "EXTERNAL_SERVICE_RATE_LIMITED";

    /**
     * HTTP error từ external service (4xx, 5xx).
     * HTTP Status: 502 Bad Gateway
     */
    public static final String EXTERNAL_SERVICE_HTTP_ERROR = "EXTERNAL_SERVICE_HTTP_ERROR";

    /**
     * Network connectivity issues với external service.
     * HTTP Status: 502 Bad Gateway
     */
    public static final String EXTERNAL_SERVICE_NETWORK_ERROR = "EXTERNAL_SERVICE_NETWORK_ERROR";

    /**
     * Authentication/Authorization error với external service.
     * HTTP Status: 502 Bad Gateway
     */
    public static final String EXTERNAL_SERVICE_AUTH_ERROR = "EXTERNAL_SERVICE_AUTH_ERROR";
}
