package vn.osp.common.infrastructure.exceptions;

import jakarta.annotation.Nullable;
import lombok.Getter;
import vn.osp.common.infrastructure.constants.ErrorCodesConst;

import java.time.Duration;
import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * Exception được ném khi có lỗi với external services trong Infrastructure Layer.
 * Bao gồm network issues, service unavailable, timeouts, rate limiting.
 * Đây là Infrastructure concern, không phải Domain concern.
 * <p>
 * HTTP Status codes:
 * - 502 Bad Gateway
 * - 503 Service Unavailable
 * - 504 Gateway Timeout
 * - 429 Too Many Requests
 */
@Getter
public class ExternalServiceException extends RuntimeException {

    private final String serviceName;
    private final String errorCode;
    private final List<Object> parameters;

    /**
     * Khởi tạo external service exception với service name và error message.
     *
     * @param serviceName tên service (Payment Gateway, Email Service, etc.)
     * @param message     thông điệp lỗi
     */
    public ExternalServiceException(String serviceName, String message) {
        super("External service '" + serviceName + "' error: " + message);
        this.errorCode = ErrorCodesConst.EXTERNAL_SERVICE_ERROR;
        this.serviceName = serviceName;
        this.parameters = List.of(serviceName, message);
    }

    /**
     * Khởi tạo với inner exception.
     *
     * @param serviceName tên service
     * @param message     thông điệp lỗi
     * @param cause       exception gốc
     */
    public ExternalServiceException(String serviceName, String message, Throwable cause) {
        super("External service '" + serviceName + "' error: " + message, cause);
        this.serviceName = serviceName;
        this.errorCode = ErrorCodesConst.EXTERNAL_SERVICE_ERROR;
        this.parameters = List.of(serviceName, message);
    }

    /**
     * Internal constructor với custom error code.
     */
    private ExternalServiceException(String errorCode, String message, Object... parameters) {
        super(message);
        this.serviceName = parameters != null && parameters.length > 0 && parameters[0] != null
                ? parameters[0].toString()
                : "Unknown";
        this.errorCode = errorCode;
        this.parameters = parameters != null ? List.copyOf(Arrays.asList(parameters)) : Collections.emptyList();
    }

    // ============================================================
    // Static Factory Methods
    // ============================================================

    /**
     * Khởi tạo với service unavailable context.
     *
     * @param serviceName tên service
     * @param retryAfter  thời gian có thể retry (optional)
     */
    public static ExternalServiceException serviceUnavailable(String serviceName, @Nullable Duration retryAfter) {
        String retryInfo = retryAfter != null
                ? " Retry after: " + retryAfter.toMinutes() + " minutes."
                : "N/A";
        return new ExternalServiceException(
                ErrorCodesConst.EXTERNAL_SERVICE_UNAVAILABLE,
                "Service '" + serviceName + "' is currently unavailable." + retryInfo,
                serviceName,
                retryAfter
        );
    }

    /**
     * Khởi tạo với timeout context.
     *
     * @param serviceName     tên service
     * @param timeoutDuration thời gian timeout
     */
    public static ExternalServiceException timeout(String serviceName, Duration timeoutDuration) {
        return new ExternalServiceException(
                ErrorCodesConst.EXTERNAL_SERVICE_TIMEOUT,
                "Service '" + serviceName + "' request timed out after " + timeoutDuration.toSeconds() + " seconds.",
                serviceName,
                timeoutDuration
        );
    }

    /**
     * Khởi tạo với rate limiting context.
     *
     * @param serviceName tên service
     * @param resetTime   thời gian reset rate limit (optional)
     */
    public static ExternalServiceException rateLimited(String serviceName, @Nullable OffsetDateTime resetTime) {
        String resetInfo = resetTime != null
                ? " Rate limit resets at: " + resetTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "."
                : "N/A";
        return new ExternalServiceException(
                ErrorCodesConst.EXTERNAL_SERVICE_RATE_LIMITED,
                "Service '" + serviceName + "' rate limit exceeded." + resetInfo,
                serviceName,
                resetTime
        );
    }

    /**
     * Khởi tạo với HTTP client error context.
     *
     * @param serviceName     tên service
     * @param httpStatusCode  HTTP status code nhận được
     * @param responseContent response content (optional)
     */
    public static ExternalServiceException httpError(String serviceName, int httpStatusCode, @Nullable String responseContent) {
        StringBuilder message = new StringBuilder("Service '" + serviceName + "' returned HTTP " + httpStatusCode);
        if (responseContent != null && !responseContent.isBlank()) {
            message.append(". Response: ").append(responseContent);
        }
        return new ExternalServiceException(
                ErrorCodesConst.EXTERNAL_SERVICE_HTTP_ERROR,
                message.toString(),
                serviceName,
                httpStatusCode,
                responseContent != null ? responseContent : "N/A"
        );
    }

    /**
     * Khởi tạo với network connectivity error.
     *
     * @param serviceName  tên service
     * @param networkError chi tiết lỗi network
     */
    public static ExternalServiceException networkError(String serviceName, String networkError) {
        return new ExternalServiceException(
                ErrorCodesConst.EXTERNAL_SERVICE_NETWORK_ERROR,
                "Network error when connecting to service '" + serviceName + "': " + networkError,
                serviceName,
                networkError
        );
    }

    /**
     * Khởi tạo với authentication error.
     *
     * @param serviceName tên service
     * @param authIssue   chi tiết vấn đề authentication
     */
    public static ExternalServiceException authenticationError(String serviceName, String authIssue) {
        return new ExternalServiceException(
                ErrorCodesConst.EXTERNAL_SERVICE_AUTH_ERROR,
                "Authentication error with service '" + serviceName + "': " + authIssue,
                serviceName,
                authIssue
        );
    }
}