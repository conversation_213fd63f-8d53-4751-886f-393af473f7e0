name: PR Title Check

on:
  pull_request:
    types: [opened, edited, synchronize]

jobs:
  check-title:
    runs-on: ubuntu-latest
    steps:
      - name: Check PR title
        uses: actions/github-script@v7
        with:
          script: |
            const allowedPrefixes = [
              '[FEATURE]', '[FIX]', '[HOTFIX]', '[TEST]', '[REFACTOR]',
              '[PERF]', '[CHORE]', '[CI]', '[RELEASE]', '[DOCS]'
            ];
            const prTitle = context.payload.pull_request.title;
            if (!allowedPrefixes.some(prefix => prTitle.startsWith(prefix))) {
              core.setFailed(
                `PR title must start with one of: ${allowedPrefixes.join(', ')}`
              );
            }
