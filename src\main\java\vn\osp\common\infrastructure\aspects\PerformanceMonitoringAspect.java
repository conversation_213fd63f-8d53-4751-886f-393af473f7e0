package vn.osp.common.infrastructure.aspects;

import lombok.extern.slf4j.Slf4j;
import org.aspectj.lang.ProceedingJoinPoint;
import org.aspectj.lang.annotation.Around;
import org.aspectj.lang.annotation.Aspect;
import org.springframework.stereotype.Component;
import vn.osp.common.infrastructure.annotations.Monitored;

@Aspect
@Component
@Slf4j
public class PerformanceMonitoringAspect {
    @Around("@annotation(monitored)")
    public Object logExecutionTime(ProceedingJoinPoint joinPoint, Monitored monitored) throws Throwable {
        long startTime = System.currentTimeMillis();

        String methodName = joinPoint.getSignature().getName();

        try {
            Object result = joinPoint.proceed();
            long executionTime = System.currentTimeMillis() - startTime;

            if (executionTime > monitored.threshold()) {
                log.warn("PERFORMANCE_WARNING: Method {} took {}ms (threshold: {}ms)",
                        methodName, executionTime, monitored.threshold());
            } else {
                log.debug("PERFORMANCE: Method {} took {}ms", methodName, executionTime);
            }

            return result;
        } catch (Exception e) {
            long executionTime = System.currentTimeMillis() - startTime;

            log.error("PERFORMANCE_ERROR: Method {} failed after {}ms", methodName, executionTime, e);

            throw e;
        }
    }
}
