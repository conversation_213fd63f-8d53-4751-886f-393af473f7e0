name: "<PERSON><PERSON><PERSON> bướ<PERSON> thiết lập cho Copilot"

# Tự động chạy các setup steps khi có thay đổi để dễ dàng validation, và
# cho phép manual testing thông qua tab "Actions" của repository
on:
  workflow_dispatch:
  push:
    paths:
      - .github/workflows/copilot-setup-steps.yml
  pull_request:
    paths:
      - .github/workflows/copilot-setup-steps.yml

jobs:
  # Job BẮT BUỘC phải có tên `copilot-setup-steps` nếu không Copilot sẽ không nhận diện được.
  copilot-setup-steps:
    runs-on: ubuntu-latest
    permissions:
      contents: read
    steps:
      - name: <PERSON><PERSON><PERSON> tra mã nguồn
        uses: actions/checkout@v4
      - name: Thiết lập Java 21
        uses: actions/setup-java@v4
        with:
          java-version: "21"
          distribution: "temurin"
      - name: Cache Maven packages
        uses: actions/cache@v4
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-
      - name: <PERSON><PERSON><PERSON> tra phiên bản môi trường
        run: |
          echo "=== Phiên bản Môi trường ==="
          echo "Phiên bản Java:"
          java --version
          echo "Phiên bản Maven:"
          mvn --version
          echo "============================="
      - name: Cài đặt dependencies Java
        run: |
          echo "📦 Đang cài đặt Java Backend dependencies..."
          mvn clean compile -DskipTests
          echo "✅ Java Backend dependencies đã được cài đặt thành công!"
        if: hashFiles('**/pom.xml') != ''
