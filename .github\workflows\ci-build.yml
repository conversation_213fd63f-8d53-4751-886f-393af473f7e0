name: CI Build

permissions:
  contents: read
  pull-requests: read

on:
  pull_request:
    types: [opened, synchronize, reopened, ready_for_review, labeled, unlabeled]

concurrency:
  group: ci-build-${{ github.event_name }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  # Kiểm tra điều kiện để bỏ qua draft PR
  check-conditions:
    name: Ki<PERSON>m tra Điều kiện Build
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    outputs:
      should-build: ${{ steps.check.outputs.should-build }}
    steps:
      - name: <PERSON><PERSON><PERSON> tra có nên build không
        id: check
        run: |
          set -euo pipefail
          echo "🔍 Kiểm tra điều kiện build..."
          echo "Event: ${{ github.event_name }}"
          echo "Ref: ${{ github.ref }}"
          echo "Base Ref: ${{ github.base_ref }}"

          # CI build chỉ chạy trên PR events, không chạy trên push events
          # Điều này tránh duplicate builds khi merge PR vào main/develop

          # Chỉ xử lý PR events
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            echo "🔀 Đã phát hiện Pull Request event"

            # Kiểm tra sự tồn tại của PR object
            if [[ -z "${{ github.event.pull_request.number }}" ]]; then
              echo "❌ Event type là 'pull_request' nhưng không tìm thấy dữ liệu PR."
              echo "Điều này có thể do action không được hỗ trợ (ví dụ: 'closed')."
              echo "Bỏ qua build để tránh lỗi."
              echo "should-build=false" >> $GITHUB_OUTPUT
              exit 0
            fi

            echo "Trạng thái Draft PR: ${{ github.event.pull_request.draft }}"
            echo "Branch đích: ${{ github.event.pull_request.base.ref }}"

            # Kiểm tra draft status trước
            if [[ "${{ github.event.pull_request.draft }}" == "true" ]]; then
              echo "📝 Phát hiện Draft PR - bỏ qua build"
              echo "should-build=false" >> $GITHUB_OUTPUT
              exit 0
            fi

            # Kiểm tra target branch
            TARGET_BRANCH="${{ github.event.pull_request.base.ref }}"
            echo "🎯 Phân tích branch đích: $TARGET_BRANCH"

            if [[ "$TARGET_BRANCH" == "main" || "$TARGET_BRANCH" == "develop" ]]; then
              echo "✅ PR hướng tới branch được bảo vệ ($TARGET_BRANCH) - bắt buộc chạy build"
              echo "should-build=true" >> $GITHUB_OUTPUT
              exit 0
            else
              echo "🔍 PR hướng tới branch khác ($TARGET_BRANCH) - kiểm tra label 'ci-build'"

              # Kiểm tra label ci-build
              LABELS='${{ toJson(github.event.pull_request.labels.*.name) }}'
              echo "Labels: $LABELS"

              if echo "$LABELS" | grep -q "ci-build"; then
                echo "🏷️ Label 'ci-build' được tìm thấy - sẽ chạy build"
                echo "should-build=true" >> $GITHUB_OUTPUT
              else
                echo "⏭️ Không có label 'ci-build' - bỏ qua build"
                echo "should-build=false" >> $GITHUB_OUTPUT
              fi
              exit 0
            fi
          fi

          # Chỉ chạy trên PR events, các event khác bỏ qua
          echo "⚠️ Event type không được hỗ trợ: ${{ github.event_name }}"
          echo "⏭️ CI Build chỉ được kích hoạt cho pull_request events. Event types được hỗ trợ: [opened, synchronize, reopened, ready_for_review, labeled, unlabeled]."
          echo "should-build=false" >> $GITHUB_OUTPUT

  skip-build-notification:
    name: Thông báo Bỏ qua Build
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: check-conditions
    if: needs.check-conditions.outputs.should-build == 'false'

    steps:
      - name: Thông báo Bỏ qua Build
        run: |
          echo "⏭️ CI Build được bỏ qua"
          echo ""
          echo "📋 Điều kiện build:"
          echo "✅ PR vào main/develop: Luôn chạy build"
          echo "🏷️ PR vào branch khác: Cần label 'ci-build'"
          echo "📝 Draft PR: Luôn bỏ qua"
          echo ""
          echo "💡 Gợi ý:"
          echo "- Thêm label 'ci-build' vào PR để kích hoạt build"
          echo "- Chuyển PR sang 'Ready for Review' nếu đang ở draft"
          echo "✅ Workflow hoàn thành thành công (đã bỏ qua)"

  detect-changes:
    name: Phát hiện Thay đổi File
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: check-conditions
    if: needs.check-conditions.outputs.should-build == 'true'
    outputs:
      backend-changed: ${{ steps.changes.outputs.backend }}
      business-docs-changed: ${{ steps.changes.outputs.business-docs }}
      meta-docs-changed: ${{ steps.changes.outputs.meta-docs }}
      requirements-review-changed: ${{ steps.changes.outputs.requirements-review }}
      ci-changed: ${{ steps.changes.outputs.ci }}
      pure-requirements-review: ${{ steps.pure-review-check.outputs.pure_requirements_review }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Phát hiện thay đổi file
        uses: dorny/paths-filter@v3
        id: changes
        with:
          filters: |
            backend:
              - 'src/**'
              - '**/*.java'
              - '**/pom.xml'
              - '**/mvnw'
              - '**/mvnw.cmd'
              - '**/application.yml'
              - '**/application.properties'
              - 'docs/backend/**'
            business-docs:
              - 'docs/requirements/**'
              - '!docs/requirements/manual-trigger-test.md'
              - '!docs/requirements/*-test.md'
              - 'docs/architecture/**'
              - 'docs/software-design/**'
              - 'docs/detailed-design/**'
            meta-docs:
              - 'docs/guidelines/**'
              - 'docs/issues/**'
              - 'docs/*.md'
            requirements-review:
              - 'docs/requirements/manual-trigger-test.md'
              - 'docs/requirements/*-test.md'
              - '.github/workflows/requirements-change-review.yml'
              - '.github/actions/requirements-review/**'
            ci:
              - '.github/workflows/**'
              - '!.github/workflows/requirements-change-review.yml'

      - name: Kiểm tra pure requirements review
        id: pure-review-check
        run: |
          PURE_REVIEW="false"
          if [[ "${{ steps.changes.outputs['requirements-review'] }}" == "true" &&
                "${{ steps.changes.outputs.backend }}" == "false" &&
                "${{ steps.changes.outputs['business-docs'] }}" == "false" ]]; then
            PURE_REVIEW="true"
            echo "🔍 Phát hiện thay đổi pure requirements review workflow - các build sẽ được bỏ qua"
          fi
          echo "pure_requirements_review=${PURE_REVIEW}" >> $GITHUB_OUTPUT

      - name: Ghi log các thay đổi được phát hiện
        run: |
          echo "🔍 Kết quả Phát hiện Thay đổi File:"
          echo "Thay đổi Backend: ${{ steps.changes.outputs.backend }}"
          echo "Thay đổi Business docs: ${{ steps.changes.outputs['business-docs'] }}"
          echo "Thay đổi Meta docs: ${{ steps.changes.outputs['meta-docs'] }}"
          echo "Thay đổi Requirements review: ${{ steps.changes.outputs['requirements-review'] }}"
          echo "Thay đổi CI: ${{ steps.changes.outputs.ci }}"
          echo "Pure requirements review: ${{ steps['pure-review-check'].outputs.pure_requirements_review }}"

  build-backend:
    name: Build Backend (Java 21)
    uses: ./.github/workflows/reusable-flexible-runner.yml
    needs: [check-conditions, detect-changes]
    if: |
      needs.check-conditions.outputs.should-build == 'true' &&
      needs.detect-changes.outputs.backend-changed == 'true'
    with:
      job_name: "Build Backend (Java 21)"
      run_command: |
        echo "🔨 Building Backend - Lý do:"
        echo "Thay đổi Backend: ${{ needs.detect-changes.outputs['backend-changed'] }}"
        echo "Thay đổi Business docs: ${{ needs.detect-changes.outputs['business-docs-changed'] }}"
        echo "---"
        echo "🔨 Đang build Java project..."
        mvn clean compile
        mvn test
        mvn package -DskipTests
        echo "✅ Backend build hoàn thành thành công!"

  build-status:
    name: Tóm tắt Trạng thái Build
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes, build-backend]
    if: always() && needs.check-conditions.outputs.should-build == 'true'

    steps:
      - name: Tóm tắt Build Thông minh
        run: |
          echo "📊 Kết quả Java Backend Build:"
          echo ""
          echo "🔍 Phát hiện Thay đổi File:"
          echo "  Backend: ${{ needs.detect-changes.outputs['backend-changed'] }}"
          echo "  Business Docs: ${{ needs.detect-changes.outputs['business-docs-changed'] }}"
          echo "  Meta Docs: ${{ needs.detect-changes.outputs['meta-docs-changed'] }}"
          echo "  Requirements Review: ${{ needs.detect-changes.outputs['requirements-review-changed'] }}"
          echo "  CI: ${{ needs.detect-changes.outputs['ci-changed'] }}"
          echo "  Pure Requirements Review: ${{ needs.detect-changes.outputs['pure-requirements-review'] }}"
          echo ""
          echo "📋 Điều kiện Kích hoạt Build:"
          echo "  Backend builds khi: backend-changed == true"
          echo ""

          # Check if pure requirements review
          if [[ "${{ needs.detect-changes.outputs['pure-requirements-review'] }}" == "true" ]]; then
            echo "🔍 Phát hiện Pure Requirements Review:"
            echo "⏭️ Backend Build: Đã bỏ qua (pure requirements review workflow)"
            echo ""
            echo "💡 Đây có vẻ là kích hoạt requirements review workflow."
            echo "   Không cần build code cho các thay đổi workflow/tài liệu thuần túy."
            BACKEND_SHOULD_BUILD="false"
          else
            # Backend status - khớp với điều kiện thực tế của build-backend job
            BACKEND_SHOULD_BUILD="false"
            if [[ "${{ needs.detect-changes.outputs['backend-changed'] }}" == "true" ]]; then
              BACKEND_SHOULD_BUILD="true"
              echo "🔨 Backend Build: ${{ needs.build-backend.result }}"
            else
              echo "⏭️ Backend Build: Đã bỏ qua (không phát hiện thay đổi backend)"
            fi
          fi

          echo ""

          # Check overall success
          OVERALL_SUCCESS="true"

          if [[ "$BACKEND_SHOULD_BUILD" == "true" && "${{ needs.build-backend.result }}" != "success" ]]; then
            OVERALL_SUCCESS="false"
            echo "❌ Backend build được yêu cầu nhưng đã thất bại!"
          fi

          if [[ "$OVERALL_SUCCESS" == "true" ]]; then
            echo "🎉 Java Backend build hoàn thành thành công!"
            echo "💡 Selective building dựa trên path đã tối ưu thời gian và tài nguyên CI!"
          else
            echo "💥 Java Backend build thất bại!"
            exit 1
          fi

  no-changes-notification:
    name: Thông báo Không có Thay đổi
    runs-on: ${{ vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
    needs: [check-conditions, detect-changes]
    if: |
      needs.check-conditions.outputs.should-build == 'true' &&
      needs.detect-changes.outputs.backend-changed == 'false' &&
      needs.detect-changes.outputs.ci-changed == 'false' &&
      needs.detect-changes.outputs.business-docs-changed == 'false' &&
      needs.detect-changes.outputs.meta-docs-changed == 'false' &&
      needs.detect-changes.outputs.requirements-review-changed == 'false'

    steps:
      - name: Thông báo Không có Thay đổi Liên quan
        run: |
          echo "🔍 Phân tích Dựa trên Path: Không Phát hiện Thay đổi Liên quan"
          echo ""
          echo "📁 Phân tích các path đã thay đổi cho thấy không có file trong:"
          echo "  ❌ src/** (file nguồn Java)"
          echo "  ❌ **/*.java (file Java)"
          echo "  ❌ **/pom.xml (file Maven)"
          echo "  ❌ **/mvnw (Maven wrapper)"
          echo "  ❌ **/application.yml (config files)"
          echo "  ❌ docs/requirements/** (business docs)"
          echo "  ❌ docs/architecture/** (business docs)"
          echo "  ❌ docs/software-design/** (business docs)"
          echo "  ❌ docs/detailed-design/** (business docs)"
          echo "  ❌ docs/guidelines/** (meta docs)"
          echo "  ❌ docs/issues/** (meta docs)"
          echo "  ❌ .github/workflows/**"
          echo ""
          echo "⏭️ Java Backend build đã bỏ qua"
          echo "💡 Tối ưu này đã tiết kiệm đáng kể thời gian và tài nguyên CI!"
          echo "✅ Workflow hoàn thành thành công (selective skip)"
