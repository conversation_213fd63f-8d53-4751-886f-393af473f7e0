name: Reusable Flexible Runner

on:
  workflow_call:
    inputs:
      runner_choice:
        description: "Runner được chỉ định từ workflow_dispatch"
        required: false
        type: string
        default: ""
      job_name:
        description: "Tên job sẽ hiển thị"
        required: false
        type: string
        default: "Flexible Runner Job"
      setup_java:
        description: "Có cần setup Java không"
        required: false
        type: boolean
        default: true
      java_version:
        description: "Phiên bản Java"
        required: false
        type: string
        default: "21"
      java_distribution:
        description: "Phân phối Java (temurin, adopt, etc.)"
        required: false
        type: string
        default: "temurin"
      working_directory:
        description: "Thư mục làm việc"
        required: false
        type: string
        default: "."
      run_command:
        description: "Command cần chạy"
        required: false
        type: string
        default: 'echo "Không có command được chỉ định"'
      install_dependencies:
        description: "Có cài dependencies không"
        required: false
        type: boolean
        default: true
      cache_dependencies:
        description: "Có cache dependencies không"
        required: false
        type: boolean
        default: true
    secrets:
      LARK_CHAT_GROUP_NOTIFICATION:
        description: "Lark webhook URL (nếu cần)"
        required: false
    outputs:
      runner_used:
        description: "Runner đã được sử dụng"
        value: ${{ jobs['flexible-runner'].outputs.runner_used }}
      job_result:
        description: "Kết quả job"
        value: ${{ jobs['flexible-runner'].outputs.job_result }}

jobs:
  flexible-runner:
    name: ${{ inputs.job_name }}
    defaults:
      run:
        shell: bash

    # Logic chọn runner linh hoạt:
    # 1. Ưu tiên input runner_choice từ workflow_dispatch
    # 2. Nếu không có, dùng repository variable DEFAULT_RUNNER_LABEL
    # 3. Fallback cuối cùng: ubuntu-latest
    runs-on: ${{ inputs.runner_choice || vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}

    outputs:
      runner_used: ${{ steps['runner-info'].outputs.runner_used }}
      job_result: ${{ steps['main-job'].outcome }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: Hiển thị Thông tin Runner
        id: runner-info
        run: |
          echo "🎉 Job đang chạy trên runner: ${{ runner.os }}-${{ runner.arch }}"
          echo "---"
          echo "🔍 Thông tin Debug:"
          echo "Lựa chọn thủ công (từ input): ${{ inputs.runner_choice }}"
          echo "Runner được cấu hình (từ var): ${{ vars.DEFAULT_RUNNER_LABEL }}"
          echo "Runner cuối cùng được sử dụng: ${{ runner.os }}"
          echo "runner_used=${{ runner.os }}" >> $GITHUB_OUTPUT

      # Conditional Java Setup
      - name: Thiết lập Java ${{ inputs.java_version }}
        if: inputs.setup_java == true
        uses: actions/setup-java@v4
        with:
          java-version: ${{ inputs.java_version }}
          distribution: ${{ inputs.java_distribution }}

      # Cache Maven dependencies (if Java is enabled)
      - name: Cache Maven packages
        if: inputs.setup_java == true && inputs.cache_dependencies == true
        uses: actions/cache@v4
        with:
          path: ~/.m2/repository
          key: ${{ runner.os }}-maven-${{ hashFiles('**/pom.xml') }}
          restore-keys: |
            ${{ runner.os }}-maven-

      # Verify Environment Versions
      - name: Kiểm tra phiên bản môi trường
        if: inputs.setup_java == true
        run: |
          echo "=== Phiên bản Môi trường ==="
          SETUP_JAVA="${{ inputs.setup_java }}"

          if [[ "$SETUP_JAVA" == "true" ]]; then
            echo "Phiên bản Java:"
            java --version
            echo "Phiên bản Maven:"
            mvn --version
          fi
          echo "============================="

      # Install Backend Dependencies (Java)
      - name: Cài đặt Dependencies Backend (Java)
        if: inputs.setup_java == true && inputs.install_dependencies == true
        working-directory: ${{ inputs.working_directory }}
        run: |
          echo "📦 Đang cài đặt Java Backend dependencies..."
          mvn clean compile -DskipTests
          echo "✅ Java Backend dependencies đã được cài đặt thành công!"

      # Main Job Execution
      - name: Thực thi Command Chính
        id: main-job
        working-directory: ${{ inputs.working_directory }}
        run: |
          echo "🚀 Đang thực thi command chính..."
          echo "Command: ${{ inputs.run_command }}"
          echo "Thư mục làm việc: ${{ inputs.working_directory }}"
          echo "---" 
          ${{ inputs.run_command }}
