package vn.osp.common.application.constants;

/**
 * Static class chứa các error codes cho Application Layer exceptions.
 * Giúp standardize error handling cho authentication và application-level concerns.
 */
public final class ErrorCodesConst {

    private ErrorCodesConst() {
        // Ngăn không cho khởi tạo class
    }

    /**
     * User ch<PERSON><PERSON> đ<PERSON> authenticate, cần đăng nhập.
     * HTTP Status: 401 Unauthorized
     */
    public static final String AUTHENTICATION_REQUIRED = "AUTHENTICATION_REQUIRED";

    /**
     * Token đã hết hạn, cần refresh hoặc login lại.
     * HTTP Status: 401 Unauthorized
     */
    public static final String TOKEN_EXPIRED = "TOKEN_EXPIRED";

    /**
     * Token không hợp lệ hoặc bị corrupt.
     * HTTP Status: 401 Unauthorized
     */
    public static final String TOKEN_INVALID = "TOKEN_INVALID";

    /**
     * Username hoặc password không đúng.
     * HTTP Status: 401 Unauthorized
     */
    public static final String INVALID_CREDENTIALS = "INVALID_CREDENTIALS";

    /**
     * Account bị khóa do quá nhiều lần login sai.
     * HTTP Status: 401 Unauthorized
     */
    public static final String ACCOUNT_LOCKED = "ACCOUNT_LOCKED";

    /**
     * Session đã hết hạn, cần login lại.
     * HTTP Status: 401 Unauthorized
     */
    public static final String SESSION_EXPIRED = "SESSION_EXPIRED";
}