# GlobalExceptionHandler trong Java Spring Boot

## Tổng quan

`GlobalExceptionHandler` là một component quan trọng trong kiến trúc Clean Architecture của dự án, nằm trong tầng **API Layer**. Class này sử dụng annotation `@RestControllerAdvice` để xử lý các exception một cách tập trung cho toàn bộ ứng dụng Spring Boot.

## Đặc điểm chính

### 1. Annotations và Dependencies

```java
@Slf4j
@RestControllerAdvice  
@RequiredArgsConstructor
```

- `@RestControllerAdvice`: Đ<PERSON>h dấu class như một global exception handler cho REST APIs
- `@Slf4j`: Tự động inject logger từ Lombok
- `@RequiredArgsConstructor`: Tự động tạo constructor với final fields

### 2. Dependencies

- `Environment`: Đ<PERSON> kiểm tra profile hiện tại (dev/prod)
- `MessageSource`: Đ<PERSON> internationalization (i18n) các thông báo lỗi

## Cấu trúc và Chức năng

### 1. Constants

```java
private static final String EXT_ERRORS = "errors";
private static final String EXT_ERROR_CODE = "errorCode";
private static final String EXT_CORRELATION_ID = "correlationId";
private static final String EXT_TIMESTAMP = "timestamp";
private static final String EXT_PARAMETERS = "parameters";
```

Các constants được sử dụng để thêm metadata vào response `ProblemDetail`.

### 2. Exception Handlers

#### 2.1. Domain Layer Exceptions

| Exception | HTTP Status | Mô tả |
|-----------|-------------|-------|
| `EntityNotFoundException` | 404 NOT_FOUND | Entity không tồn tại |
| `BusinessRuleException` | 422 UNPROCESSABLE_ENTITY | Vi phạm business rule |
| `ConcurrencyException` | 409 CONFLICT | Xung đột concurrency |
| `DuplicateEntityException` | 409 CONFLICT | Entity bị trùng lặp |
| `InvalidOperationDomainException` | 422 UNPROCESSABLE_ENTITY | Operation không hợp lệ |
| `ForbiddenDomainException` | 403 FORBIDDEN | Không có quyền truy cập |

#### 2.2. Application Layer Exceptions

| Exception | HTTP Status | Mô tả |
|-----------|-------------|-------|
| `UnauthorizedException` | 401 UNAUTHORIZED | Chưa xác thực |

#### 2.3. Infrastructure Layer Exceptions

| Exception | HTTP Status | Mô tả |
|-----------|-------------|-------|
| `ExternalServiceException` | Dynamic | Lỗi từ external service |

**Dynamic Status cho ExternalServiceException:**
- `EXTERNAL_SERVICE_UNAVAILABLE` → 503 SERVICE_UNAVAILABLE
- `EXTERNAL_SERVICE_TIMEOUT` → 504 GATEWAY_TIMEOUT  
- `EXTERNAL_SERVICE_RATE_LIMITED` → 429 TOO_MANY_REQUESTS
- Default → 502 BAD_GATEWAY

#### 2.4. Framework Exceptions

| Exception | HTTP Status | Mô tả |
|-----------|-------------|-------|
| `ConstraintViolationException` | 400 BAD_REQUEST | Validation error |
| `NoSuchElementException` | 404 NOT_FOUND | Key không tồn tại |
| `OptimisticLockException` | 409 CONFLICT | Optimistic locking conflict |
| `DataIntegrityViolationException` | 400 BAD_REQUEST | Database constraint violation |
| `Exception` (fallback) | 500 INTERNAL_SERVER_ERROR | Unhandled exception |

### 3. Response Format - RFC 7807 Problem Details

Tất cả exception handlers đều trả về `ProblemDetail` theo chuẩn RFC 7807:

```json
{
  "type": "https://tools.ietf.org/html/rfc7231#section-6.5.4",
  "title": "Entity Not Found", 
  "status": 404,
  "detail": "User with ID 123 not found",
  "errorCode": "USR_001",
  "parameters": ["123"],
  "correlationId": "550e8400-e29b-41d4-a716-************",
  "timestamp": "2024-01-15T10:30:00Z"
}
```

**Các fields bổ sung:**
- `errorCode`: Mã lỗi tùy chỉnh từ business exception
- `parameters`: Parameters từ exception
- `correlationId`: UUID để trace request
- `timestamp`: Thời gian xảy ra lỗi
- `errors`: Chi tiết validation errors (cho ConstraintViolationException)

### 4. Security và Environment Handling

```java
private void enrichCommon(ProblemDetail problem) {
    if (!isDevelopment()) {
        problem.setDetail(null); // hide sensitive details
    }
    
    problem.setProperty(EXT_CORRELATION_ID, java.util.UUID.randomUUID().toString());
    problem.setProperty(EXT_TIMESTAMP, OffsetDateTime.now());
}

private boolean isDevelopment() {
    return Set.of(environment.getActiveProfiles()).contains("dev");
}
```

**Bảo mật:**
- Trong môi trường production, chi tiết lỗi (`detail`) sẽ bị ẩn để tránh leak thông tin nhạy cảm
- Chỉ hiển thị chi tiết lỗi trong môi trường development

## Internationalization (i18n)

Handler sử dụng `MessageSource` để hỗ trợ đa ngôn ngữ:

```java
problem.setTitle(messageSource.getMessage("V0001_FluentValidationResponseTitle", null, LocaleContextHolder.getLocale()));
```

**Message keys được sử dụng:**
- `V0001_FluentValidationResponseTitle`: Tiêu đề cho validation errors
- `V0002_OptimisticLockExceptionMessage`: Thông báo optimistic lock
- `V0003_UnhandledException`: Thông báo unhandled exception  
- `V0004_DbUpdateExceptionMessage`: Thông báo database update error

## Best Practices

### 1. Clean Architecture Compliance
- Tách biệt rõ ràng exception handling theo từng layer
- Domain exceptions không phụ thuộc vào HTTP status codes
- API layer chịu trách nhiệm mapping exceptions thành HTTP responses

### 2. Security
- Ẩn thông tin nhạy cảm trong production
- Sử dụng correlation ID để tracking mà không expose internal information

### 3. Standardization  
- Tuân thủ RFC 7807 cho problem details
- Consistent error response format
- Proper HTTP status codes theo RFC specifications

### 4. Maintainability
- Centralized exception handling
- Clear separation of concerns
- Easy to extend với custom exceptions mới

## Cách sử dụng

### 1. Trong Domain Layer
```java
throw new EntityNotFoundException("USR_001", "User not found", userId);
```

### 2. Trong Application Layer  
```java
throw new UnauthorizedException("AUTH_001", "Invalid credentials");
```

### 3. Trong Infrastructure Layer
```java
throw new ExternalServiceException(
    ErrorCodesConst.EXTERNAL_SERVICE_TIMEOUT, 
    "Payment service timeout"
);
```

## Kết luận

`GlobalExceptionHandler` cung cấp một giải pháp robust và standardized cho exception handling trong Spring Boot application, tuân thủ các nguyên tắc Clean Architecture và best practices về security, maintainability.
