name: Flexible Runner Workflow

on:
  # Cho phép chạy workflow thủ công từ tab Actions
  workflow_dispatch:
    inputs:
      runner_choice:
        description: "Chọn runner để chạy job"
        required: true
        type: choice
        # Sử dụng trực tiếp nhãn runner thật để đơn giản hóa
        options:
          - ubuntu-latest
          - mac-self-hosted
          - linux-self-hosted
        default: "ubuntu-latest"
      job_type:
        description: "Loại job cần chạy"
        required: false
        type: choice
        options:
          - build-only
          - build-with-java
        default: "build-only"

  # Ví dụ: cũng có thể trigger khi push vào branch main
  push:
    branches:
      - main

jobs:
  # Job sử dụng reusable workflow
  flexible-runner-job:
    name: Demo Flexible Runner
    uses: ./.github/workflows/reusable-flexible-runner.yml
    with:
      runner_choice: ${{ github.event.inputs.runner_choice || vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}
      job_name: "Build trên Runner đã chọn"
      setup_java: ${{ github.event.inputs.job_type == 'build-with-java' }}
      install_dependencies: ${{ github.event.inputs.job_type == 'build-with-java'}}
      run_command: |
        echo "🚀 Đang chạy demo flexible runner..."
        echo "Loại job: ${{ github.event.inputs.job_type }}"
        echo "Runner đã chọn: ${{ github.event.inputs.runner_choice || vars.DEFAULT_RUNNER_LABEL || 'ubuntu-latest' }}"
        echo "✅ Demo hoàn thành thành công!"
