package vn.osp.common.domain.exceptions;

import vn.osp.common.domain.constants.ErrorCodesConst;

import java.util.Map;
import java.util.stream.Collectors;

/**
 * Exception được ném khi entity không được tìm thấy trong hệ thống.
 * Tương ứng với HTTP 404 Not Found status code.
 */
public class EntityNotFoundException extends DomainException {

    /**
     * Khởi tạo entity not found exception với entity name và ID.
     *
     * <pre>
     * throw new EntityNotFoundException("User", userId);
     * throw new EntityNotFoundException("Product", productCode);
     * </pre>
     *
     * @param entityName tên của entity (thường là class name)
     * @param entityId   ID của entity không tìm thấy
     */
    public EntityNotFoundException(String entityName, Object entityId) {
        super(
                ErrorCodesConst.ENTITY_NOT_FOUND,
                "%s with identifier '%s' was not found.".formatted(entityName, entityId),
                entityName,
                entityId
        );
    }

    /**
     * Khởi tạo entity not found exception với custom message.
     *
     * @param entityName    tên của entity
     * @param entityId      ID của entity
     * @param customMessage thông điệp tùy chỉnh
     */
    public EntityNotFoundException(String entityName, Object entityId, String customMessage) {
        super(ErrorCodesConst.ENTITY_NOT_FOUND, customMessage, entityName, entityId);
    }

    /**
     * Khởi tạo với multiple search criteria.
     *
     * <pre>
     * Map&lt;String, Object&gt; criteria = Map.of("Email", email, "Status", "Active");
     * throw new EntityNotFoundException("User", criteria);
     * </pre>
     *
     * @param entityName     tên của entity
     * @param searchCriteria điều kiện tìm kiếm dưới dạng map
     */
    public EntityNotFoundException(String entityName, Map<String, Object> searchCriteria) {
        super(
                ErrorCodesConst.ENTITY_NOT_FOUND,
                "%s with criteria '%s' was not found."
                        .formatted(entityName,
                                searchCriteria.entrySet().stream()
                                        .map(e -> e.getKey() + "=" + e.getValue())
                                        .collect(Collectors.joining(", "))
                        ),
                entityName,
                searchCriteria
        );
    }
}