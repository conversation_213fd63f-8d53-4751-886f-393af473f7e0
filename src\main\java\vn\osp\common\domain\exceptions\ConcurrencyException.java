package vn.osp.common.domain.exceptions;

import vn.osp.common.domain.constants.ErrorCodesConst;

import java.time.OffsetDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.stream.Collectors;

/**
 * Exception được ném khi có xung đột concurrency trong database operations.
 * Tương ứng với HTTP 409 Conflict status code.
 * Thường xảy ra với optimistic locking hoặc concurrent updates.
 */
public class ConcurrencyException extends DomainException {

    /**
     * Khởi tạo concurrency exception với entity thông tin.
     *
     * <pre>
     * throw new ConcurrencyException("Order", orderId);
     * </pre>
     *
     * @param entityName tên entity bị conflict
     * @param entityId   id của entity
     */
    public ConcurrencyException(String entityName, Object entityId) {
        super(
                ErrorCodesConst.CONCURRENCY_CONFLICT,
                entityName + " with id '" + entityId + "' has been modified by another process. Please refresh and try again.",
                entityName,
                entityId
        );
    }

    /**
     * Khởi tạo với custom message và version info.
     *
     * @param entityName      tên entity
     * @param entityId        id entity
     * @param expectedVersion version được expect
     * @param actualVersion   version thực tế trong DB
     */
    public ConcurrencyException(String entityName, Object entityId, Object expectedVersion, Object actualVersion) {
        super(
                ErrorCodesConst.CONCURRENCY_CONFLICT,
                entityName + " with id '" + entityId + "' has been modified. Expected version: "
                        + expectedVersion + ", actual version: " + actualVersion + ".",
                entityName,
                entityId,
                expectedVersion,
                actualVersion
        );
    }

    /**
     * Khởi tạo với batch operation context.
     *
     * @param operationType    loại operation (Insert, Update, Delete)
     * @param affectedEntities danh sách entities bị ảnh hưởng
     * @return ConcurrencyException instance
     */
    public static ConcurrencyException batchOperation(String operationType, EntityRef... affectedEntities) {
        String entitiesInfo = affectedEntities != null
                ? Arrays.stream(affectedEntities)
                .map(e -> e.entityName() + "(" + e.entityId() + ")")
                .collect(Collectors.joining(", "))
                : "N/A";

        return new ConcurrencyException(
                ErrorCodesConst.BATCH_CONCURRENCY_CONFLICT,
                "Batch " + operationType + " operation failed due to concurrency conflicts. Affected entities: " + entitiesInfo,
                operationType, affectedEntities
        );
    }

    /**
     * Khởi tạo với resource locking context.
     *
     * @param resourceType loại resource bị lock
     * @param resourceId   id của resource
     * @param lockedBy     user/process đang giữ lock
     * @param lockTime     thời gian bắt đầu lock
     * @return ConcurrencyException instance
     */
    public static ConcurrencyException resourceLocked(String resourceType, Object resourceId, String lockedBy, OffsetDateTime lockTime) {
        String formattedTime = lockTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));

        return new ConcurrencyException(
                ErrorCodesConst.RESOURCE_LOCKED,
                resourceType + " with id '" + resourceId + "' is currently locked by " + lockedBy + " since " + formattedTime + ".",
                resourceType,
                resourceId,
                lockedBy,
                lockTime
        );
    }

    /**
     * Private constructor cho internal factory methods.
     */
    private ConcurrencyException(String errorCode, String message, Object... parameters) {
        super(errorCode, message, parameters);
    }

    /**
     * Record đơn giản để thay thế tuple (EntityName, EntityId) từ C#.
     */
    public record EntityRef(String entityName, Object entityId) {
    }
}